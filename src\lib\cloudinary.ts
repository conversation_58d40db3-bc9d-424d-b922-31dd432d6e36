import { v2 as cloudinary } from 'cloudinary';

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

export interface CloudinaryUploadResult {
  public_id: string;
  secure_url: string;
  url: string;
  width: number;
  height: number;
  format: string;
  resource_type: string;
  bytes: number;
  created_at: string;
}

export interface UploadOptions {
  folder?: string;
  transformation?: any[];
  quality?: string | number;
  format?: string;
  width?: number;
  height?: number;
  crop?: string;
}

/**
 * Upload file buffer to Cloudinary
 */
export async function uploadToCloudinary(
  buffer: Buffer,
  options: UploadOptions = {}
): Promise<CloudinaryUploadResult> {
  const {
    folder = 'blog-images',
    transformation = [],
    quality = 'auto',
    format = 'auto',
    ...otherOptions
  } = options;

  return new Promise((resolve, reject) => {
    cloudinary.uploader.upload_stream(
      {
        folder,
        transformation,
        quality,
        format,
        ...otherOptions,
      },
      (error, result) => {
        if (error) {
          reject(error);
        } else if (result) {
          resolve(result as CloudinaryUploadResult);
        } else {
          reject(new Error('Upload failed - no result returned'));
        }
      }
    ).end(buffer);
  });
}

/**
 * Upload image from URL to Cloudinary
 */
export async function uploadFromUrl(
  imageUrl: string,
  options: UploadOptions = {}
): Promise<CloudinaryUploadResult> {
  const {
    folder = 'blog-images',
    transformation = [],
    quality = 'auto',
    format = 'auto',
    ...otherOptions
  } = options;

  try {
    // Ensure imageUrl is a string and valid
    if (typeof imageUrl !== 'string' || !imageUrl.trim()) {
      throw new Error('Invalid image URL: URL must be a non-empty string');
    }

    // Validate URL format
    try {
      new URL(imageUrl.trim());
    } catch {
      throw new Error('Invalid image URL: URL format is not valid');
    }

    const result = await cloudinary.uploader.upload(imageUrl.trim(), {
      folder,
      transformation,
      quality,
      format,
      ...otherOptions,
    });

    return result as CloudinaryUploadResult;
  } catch (error) {
    // Properly handle error object
    if (error instanceof Error) {
      throw new Error(`Failed to upload from URL: ${error.message}`);
    } else if (typeof error === 'object' && error !== null) {
      // Handle Cloudinary error objects
      const errorMessage = (error as any).message || (error as any).error || JSON.stringify(error);
      throw new Error(`Failed to upload from URL: ${errorMessage}`);
    } else {
      throw new Error(`Failed to upload from URL: ${String(error)}`);
    }
  }
}

/**
 * Delete image from Cloudinary
 */
export async function deleteFromCloudinary(publicId: string): Promise<any> {
  try {
    const result = await cloudinary.uploader.destroy(publicId);
    return result;
  } catch (error) {
    throw new Error(`Failed to delete image: ${error}`);
  }
}

/**
 * Generate optimized image URL with transformations
 */
export function generateImageUrl(
  publicId: string,
  options: {
    width?: number;
    height?: number;
    crop?: string;
    quality?: string | number;
    format?: string;
  } = {}
): string {
  const {
    width,
    height,
    crop = 'fill',
    quality = 'auto',
    format = 'auto',
  } = options;

  return cloudinary.url(publicId, {
    width,
    height,
    crop,
    quality,
    format,
    secure: true,
  });
}

/**
 * Extract public ID from Cloudinary URL
 */
export function extractPublicId(cloudinaryUrl: string): string | null {
  try {
    const regex = /\/v\d+\/(.+)\.[a-zA-Z]+$/;
    const match = cloudinaryUrl.match(regex);
    return match ? match[1] : null;
  } catch (error) {
    return null;
  }
}

/**
 * Validate Cloudinary configuration
 */
export function validateCloudinaryConfig(): boolean {
  return !!(
    process.env.CLOUDINARY_CLOUD_NAME &&
    process.env.CLOUDINARY_API_KEY &&
    process.env.CLOUDINARY_API_SECRET
  );
}

export default cloudinary;
