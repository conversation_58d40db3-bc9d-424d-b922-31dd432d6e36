'use client';

import { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Image as ImageIcon,
  Upload,
  Link,
  Cloud,
  FolderOpen,
  ChevronDown,
  Loader2,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Plus,
  Minus,
} from 'lucide-react';
import Image from 'next/image';
import { toast } from '@/hooks/use-toast';
import { Progress } from '@/components/ui/progress';

interface EnhancedToolbarImageUploaderProps {
  onImageInsert: (imageData: {
    url: string;
    alt?: string;
    title?: string;
    width?: number;
    height?: number;
    alignment?: 'left' | 'center' | 'right';
    sizeOption?: string;
  }) => void;
}

type UploadLocation = 'local' | 'cloud';
type UploadMethod = 'file' | 'url' | 'drive' | 'dropbox';

// Image size options
const imageSizeOptions = [
  { name: "ES", width: 150 },
  { name: "S", width: 300 },
  { name: "M", width: 500 },
  { name: "L", width: 800 },
  { name: "XL", width: 1024 },
  { name: "XXL", width: 1280 },
  { name: "custom", width: 0 },
];

export function EnhancedToolbarImageUploader({ onImageInsert }: EnhancedToolbarImageUploaderProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [uploadMethod, setUploadMethod] = useState<UploadMethod>('file');
  const [uploadLocation, setUploadLocation] = useState<UploadLocation>('local');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  
  // Image data states
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [imageUrl, setImageUrl] = useState('');
  const [imageAlt, setImageAlt] = useState('');
  const [imageTitle, setImageTitle] = useState('');
  const [imageWidth, setImageWidth] = useState(500);
  const [imageHeight, setImageHeight] = useState(300);
  const [imageAlignment, setImageAlignment] = useState<'left' | 'center' | 'right'>('center');
  const [imageSizeOption, setImageSizeOption] = useState('M');
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Allowed file types
  const allowedFileTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp',
    'image/gif',
  ];

  const maxFileSize = 50 * 1024 * 1024; // 50MB

  const validateFile = (file: File): boolean => {
    if (!allowedFileTypes.includes(file.type)) {
      toast({
        title: 'Invalid file type',
        description: 'Please upload a JPEG, PNG, WebP, or GIF file.',
        variant: 'destructive',
      });
      return false;
    }

    if (file.size > maxFileSize) {
      toast({
        title: 'File too large',
        description: 'Please upload an image smaller than 50MB.',
        variant: 'destructive',
      });
      return false;
    }

    return true;
  };

  const saveImageToDatabase = async (imageData: any) => {
    try {
      const response = await fetch('/api/images', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: imageData.url || imageData.fileUrl,
          publicId: imageData.publicId,
          fileName: imageData.fileName,
          location: uploadLocation,
          type: uploadMethod,
          originalUrl: uploadMethod === 'url' ? imageUrl : undefined,
          width: imageData.width,
          height: imageData.height,
          format: imageData.format,
          bytes: imageData.bytes,
          alt: imageAlt,
          title: imageTitle,
          folder: imageData.type || 'blog-content',
        }),
        credentials: 'include',
      });

      if (!response.ok) {
        console.warn('Failed to save image to database:', await response.text());
      }
    } catch (error) {
      console.warn('Failed to save image to database:', error);
    }
  };

  const uploadToDestination = async (file: File | string, method: UploadMethod): Promise<any> => {
    const endpoint = uploadLocation === 'cloud' 
      ? '/api/upload/cloudinary' 
      : '/api/upload/local';

    if (method === 'file' && file instanceof File) {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'blog-content');

      return fetch(endpoint, {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });
    } else if (method === 'url' && typeof file === 'string') {
      return fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: file,
          type: 'blog-content',
        }),
        credentials: 'include',
      });
    }

    throw new Error('Invalid upload method or file type');
  };

  const handleFileUpload = async (file: File) => {
    if (!validateFile(file)) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Create preview
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);
      setImageTitle(file.name.split('.')[0]);

      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 10;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 300);

      const response = await uploadToDestination(file, 'file');
      
      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to upload image');
      }

      const result = await response.json();
      
      // Save to database
      await saveImageToDatabase(result);
      
      // Clean up object URL
      URL.revokeObjectURL(objectUrl);
      
      toast({
        title: 'Image uploaded successfully',
        description: `Image uploaded to ${uploadLocation === 'cloud' ? 'Cloudinary' : 'VPS'}.`,
      });

    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: 'Upload failed',
        description: error instanceof Error ? error.message : 'Failed to upload image',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleUrlUpload = async () => {
    if (!imageUrl.trim()) {
      toast({
        title: 'URL required',
        description: 'Please enter a valid image URL.',
        variant: 'destructive',
      });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Set preview
      setPreviewUrl(imageUrl.trim());

      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 15;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 200);

      const response = await uploadToDestination(imageUrl.trim(), 'url');
      
      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to upload image from URL');
      }

      const result = await response.json();
      
      // Save to database
      await saveImageToDatabase(result);
      
      toast({
        title: 'Image uploaded successfully',
        description: `Image uploaded from URL to ${uploadLocation === 'cloud' ? 'Cloudinary' : 'VPS'}.`,
      });

    } catch (error) {
      console.error('URL upload error:', error);
      toast({
        title: 'Upload failed',
        description: error instanceof Error ? error.message : 'Failed to upload image from URL',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleInsertImage = () => {
    if (!previewUrl) {
      toast({
        title: 'No image selected',
        description: 'Please upload an image first.',
        variant: 'destructive',
      });
      return;
    }

    // Calculate dimensions based on size option
    let width = imageWidth;
    let height = imageHeight;

    if (imageSizeOption !== 'custom') {
      const sizeOption = imageSizeOptions.find(option => option.name === imageSizeOption);
      if (sizeOption && sizeOption.width > 0) {
        const aspectRatio = imageHeight / imageWidth;
        width = sizeOption.width;
        height = Math.round(width * aspectRatio);
      }
    }

    onImageInsert({
      url: previewUrl,
      alt: imageAlt,
      title: imageTitle,
      width: width,
      height: height,
      alignment: imageAlignment,
      sizeOption: imageSizeOption,
    });

    resetDialog();
  };

  const resetDialog = () => {
    setIsDialogOpen(false);
    setPreviewUrl(null);
    setImageUrl('');
    setImageAlt('');
    setImageTitle('');
    setImageWidth(500);
    setImageHeight(300);
    setImageAlignment('center');
    setImageSizeOption('M');
    setUploadProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const openUploadDialog = (method: UploadMethod) => {
    setUploadMethod(method);
    setIsDialogOpen(true);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const adjustImageSize = (delta: number) => {
    setImageWidth(prev => Math.max(50, Math.min(2000, prev + delta)));
    setImageHeight(prev => Math.max(50, Math.min(2000, prev + delta * 0.6))); // Maintain aspect ratio
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            type="button"
            size="sm"
            variant="ghost"
            className="h-7 px-2 gap-1"
            title="Insert Image"
          >
            <ImageIcon className="h-3.5 w-3.5" />
            <ChevronDown className="h-3 w-3" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-64">
          <div className="px-2 py-1.5 text-sm font-medium">Upload Image</div>
          <DropdownMenuSeparator />
          
          {/* Upload Location Selector */}
          <div className="px-2 py-2">
            <Label className="text-xs text-muted-foreground">Upload Location:</Label>
            <Select value={uploadLocation} onValueChange={(value: UploadLocation) => setUploadLocation(value)}>
              <SelectTrigger className="h-7 text-xs mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="local">Local (VPS)</SelectItem>
                <SelectItem value="cloud">Cloud (Cloudinary)</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem onClick={() => openUploadDialog('file')}>
            <Upload className="h-4 w-4 mr-2" />
            Upload File
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={() => openUploadDialog('url')}>
            <Link className="h-4 w-4 mr-2" />
            Upload via URL
          </DropdownMenuItem>
          
          <DropdownMenuItem disabled>
            <Cloud className="h-4 w-4 mr-2" />
            Google Drive
            <span className="ml-auto text-xs text-muted-foreground">Soon</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem disabled>
            <FolderOpen className="h-4 w-4 mr-2" />
            Dropbox
            <span className="ml-auto text-xs text-muted-foreground">Soon</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {uploadMethod === 'file' ? 'Upload File' :
               uploadMethod === 'url' ? 'Upload via URL' :
               uploadMethod === 'drive' ? 'Upload from Google Drive' :
               'Upload from Dropbox'}
            </DialogTitle>
          </DialogHeader>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column - Upload */}
            <div className="space-y-4">
              {/* Upload Progress */}
              {isUploading && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm">Uploading to {uploadLocation === 'cloud' ? 'Cloudinary' : 'VPS'}...</span>
                  </div>
                  <Progress value={uploadProgress} className="w-full" />
                </div>
              )}

              {/* Upload Method Content */}
              {uploadMethod === 'file' && (
                <div className="space-y-4">
                  <div>
                    <Label>Select Image File</Label>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept={allowedFileTypes.join(',')}
                      onChange={handleFileSelect}
                      className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
                      disabled={isUploading}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Supported: JPEG, PNG, WebP, GIF. Max size: 50MB
                    </p>
                  </div>
                </div>
              )}

              {uploadMethod === 'url' && (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="imageUrl">Image URL</Label>
                    <Input
                      id="imageUrl"
                      type="url"
                      placeholder="https://example.com/image.jpg"
                      value={imageUrl}
                      onChange={(e) => setImageUrl(e.target.value)}
                      disabled={isUploading}
                    />
                  </div>
                  <Button
                    onClick={handleUrlUpload}
                    disabled={isUploading || !imageUrl.trim()}
                    className="w-full"
                  >
                    {isUploading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Uploading...
                      </>
                    ) : (
                      'Upload from URL'
                    )}
                  </Button>
                </div>
              )}

              {/* Image Preview */}
              {previewUrl && (
                <div className="space-y-4">
                  <div className="relative border rounded-lg p-4">
                    <Image
                      src={previewUrl}
                      alt={imageAlt || 'Preview'}
                      width={400}
                      height={300}
                      className="object-contain rounded-md max-h-[300px] mx-auto"
                      style={{
                        maxWidth: '100%',
                        height: 'auto',
                      }}
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Right Column - Image Settings */}
            <div className="space-y-4">
              <h3 className="font-medium">Image Settings</h3>
              
              {/* Image Metadata */}
              <div className="space-y-3">
                <div>
                  <Label htmlFor="imageTitle">Title</Label>
                  <Input
                    id="imageTitle"
                    placeholder="Image title"
                    value={imageTitle}
                    onChange={(e) => setImageTitle(e.target.value)}
                    maxLength={200}
                  />
                </div>
                <div>
                  <Label htmlFor="imageAlt">Alt Text</Label>
                  <Input
                    id="imageAlt"
                    placeholder="Describe the image for accessibility"
                    value={imageAlt}
                    onChange={(e) => setImageAlt(e.target.value)}
                    maxLength={200}
                  />
                </div>
              </div>

              {/* Size Controls */}
              <div className="space-y-3">
                <div>
                  <Label>Size Preset</Label>
                  <Select value={imageSizeOption} onValueChange={setImageSizeOption}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {imageSizeOptions.map((option) => (
                        <SelectItem key={option.name} value={option.name}>
                          {option.name} {option.width > 0 && `(${option.width}px)`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {imageSizeOption === 'custom' && (
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <Label>Width</Label>
                      <div className="flex items-center gap-1">
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => adjustImageSize(-50)}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <Input
                          type="number"
                          value={imageWidth}
                          onChange={(e) => setImageWidth(parseInt(e.target.value) || 0)}
                          min={50}
                          max={2000}
                          className="text-center"
                        />
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => adjustImageSize(50)}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <div>
                      <Label>Height</Label>
                      <Input
                        type="number"
                        value={imageHeight}
                        onChange={(e) => setImageHeight(parseInt(e.target.value) || 0)}
                        min={50}
                        max={2000}
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Alignment Controls */}
              <div className="space-y-2">
                <Label>Alignment</Label>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant={imageAlignment === 'left' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setImageAlignment('left')}
                  >
                    <AlignLeft className="h-4 w-4 mr-1" />
                    Left
                  </Button>
                  <Button
                    type="button"
                    variant={imageAlignment === 'center' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setImageAlignment('center')}
                  >
                    <AlignCenter className="h-4 w-4 mr-1" />
                    Center
                  </Button>
                  <Button
                    type="button"
                    variant={imageAlignment === 'right' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setImageAlignment('right')}
                  >
                    <AlignRight className="h-4 w-4 mr-1" />
                    Right
                  </Button>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 pt-4">
                <Button
                  variant="outline"
                  onClick={resetDialog}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleInsertImage}
                  disabled={!previewUrl}
                  className="flex-1"
                >
                  Insert Image
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
