'use client';

import { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Upload,
  Image as ImageIcon,
  Link,
  Cloud,
  FolderOpen,
  X,
  Loader2,
  Check,
  AlertCircle
} from 'lucide-react';
import Image from 'next/image';
import { toast } from '@/hooks/use-toast';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';

interface CloudinaryImageUploaderProps {
  label: string;
  currentImageUrl?: string;
  onImageUpload: (result: {
    url: string;
    publicId: string;
    width: number;
    height: number;
    alt?: string;
    credit?: string;
  }) => void;
  type?: 'blog-cover' | 'blog-content' | 'blog-featured';
  maxFileSizeMB?: number;
  className?: string;
  showMetadata?: boolean;
}

export function CloudinaryImageUploader({
  label,
  currentImageUrl,
  onImageUpload,
  type = 'blog-content',
  maxFileSizeMB = 50,
  className = '',
  showMetadata = true,
}: CloudinaryImageUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentImageUrl || null);
  const [dragActive, setDragActive] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  
  // Metadata states
  const [imageAlt, setImageAlt] = useState('');
  const [imageCredit, setImageCredit] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const maxSizeBytes = maxFileSizeMB * 1024 * 1024;

  // Allowed file types
  const allowedFileTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml',
  ];

  const validateFile = useCallback((file: File): boolean => {
    // Check file type
    if (!allowedFileTypes.includes(file.type)) {
      toast({
        title: 'Invalid file type',
        description: 'Please upload a JPEG, PNG, GIF, WebP, or SVG file.',
        variant: 'destructive',
      });
      return false;
    }

    // Check file size
    if (file.size > maxSizeBytes) {
      toast({
        title: 'File too large',
        description: `Please upload an image smaller than ${maxFileSizeMB}MB.`,
        variant: 'destructive',
      });
      return false;
    }

    return true;
  }, [maxSizeBytes, maxFileSizeMB]);

  const uploadFile = async (file: File) => {
    if (!validateFile(file)) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Create preview
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);

      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 10;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 300);

      const response = await fetch('/api/upload/cloudinary', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to upload image');
      }

      const result = await response.json();
      
      // Clean up object URL
      URL.revokeObjectURL(objectUrl);
      
      onImageUpload({
        url: result.url,
        publicId: result.publicId,
        width: result.width,
        height: result.height,
        alt: imageAlt,
        credit: imageCredit,
      });

      toast({
        title: 'Image uploaded successfully',
        description: 'Your image has been uploaded to Cloudinary.',
      });

      setIsDialogOpen(false);
    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: 'Upload failed',
        description: error instanceof Error ? error.message : 'Failed to upload image',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const uploadFromUrl = async () => {
    if (!imageUrl.trim()) {
      toast({
        title: 'URL required',
        description: 'Please enter a valid image URL.',
        variant: 'destructive',
      });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 15;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 200);

      const response = await fetch('/api/upload/cloudinary', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: imageUrl.trim(),
          type,
        }),
        credentials: 'include',
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to upload image from URL');
      }

      const result = await response.json();
      
      setPreviewUrl(result.url);
      
      onImageUpload({
        url: result.url,
        publicId: result.publicId,
        width: result.width,
        height: result.height,
        alt: imageAlt,
        credit: imageCredit,
      });

      toast({
        title: 'Image uploaded successfully',
        description: 'Image has been uploaded from URL to Cloudinary.',
      });

      setIsDialogOpen(false);
      setImageUrl('');
    } catch (error) {
      console.error('URL upload error:', error);
      toast({
        title: 'Upload failed',
        description: error instanceof Error ? error.message : 'Failed to upload image from URL',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      uploadFile(file);
    }
  };

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
    
    const file = e.dataTransfer.files?.[0];
    if (file) {
      uploadFile(file);
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  }, []);

  const removeImage = () => {
    setPreviewUrl(null);
    setImageAlt('');
    setImageCredit('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <Label className="text-sm font-medium">{label}</Label>
      
      {/* Current Image Preview */}
      {previewUrl && (
        <div className="relative group">
          <div className="relative w-full h-48 rounded-lg overflow-hidden border">
            <Image
              src={previewUrl}
              alt="Preview"
              fill
              className="object-cover"
            />
            <Button
              type="button"
              variant="destructive"
              size="sm"
              className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={removeImage}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Upload Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button type="button" variant="outline" className="w-full">
            <Upload className="h-4 w-4 mr-2" />
            {previewUrl ? 'Change Image' : 'Upload Image'}
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Upload Image to Cloudinary</DialogTitle>
          </DialogHeader>
          
          {isUploading && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Uploading to Cloudinary...</span>
              </div>
              <Progress value={uploadProgress} className="w-full" />
            </div>
          )}

          <Tabs defaultValue="file" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="file">
                <Upload className="h-4 w-4 mr-1" />
                File
              </TabsTrigger>
              <TabsTrigger value="url">
                <Link className="h-4 w-4 mr-1" />
                URL
              </TabsTrigger>
              <TabsTrigger value="drive" disabled>
                <Cloud className="h-4 w-4 mr-1" />
                Drive
              </TabsTrigger>
              <TabsTrigger value="dropbox" disabled>
                <FolderOpen className="h-4 w-4 mr-1" />
                Dropbox
              </TabsTrigger>
            </TabsList>

            <TabsContent value="file" className="space-y-4">
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  dragActive ? 'border-primary bg-primary/5' : 'border-gray-300'
                }`}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
              >
                <ImageIcon className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p className="text-sm text-gray-600 mb-4">
                  Drag and drop an image here, or click to select
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept={allowedFileTypes.join(',')}
                  onChange={handleFileChange}
                  className="hidden"
                  disabled={isUploading}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploading}
                >
                  Select File
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="url" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="imageUrl">Image URL</Label>
                  <Input
                    id="imageUrl"
                    type="url"
                    placeholder="https://example.com/image.jpg"
                    value={imageUrl}
                    onChange={(e) => setImageUrl(e.target.value)}
                    disabled={isUploading}
                  />
                </div>
                <Button
                  type="button"
                  onClick={uploadFromUrl}
                  disabled={isUploading || !imageUrl.trim()}
                  className="w-full"
                >
                  {isUploading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      Upload from URL
                    </>
                  )}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="drive" className="space-y-4">
              <div className="text-center py-8">
                <Cloud className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p className="text-sm text-gray-600">Google Drive integration coming soon</p>
              </div>
            </TabsContent>

            <TabsContent value="dropbox" className="space-y-4">
              <div className="text-center py-8">
                <FolderOpen className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p className="text-sm text-gray-600">Dropbox integration coming soon</p>
              </div>
            </TabsContent>
          </Tabs>

          {/* Image Metadata */}
          {showMetadata && (
            <div className="space-y-4 border-t pt-4">
              <h4 className="font-medium">Image Metadata</h4>
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <Label htmlFor="imageAlt">Alt Text</Label>
                  <Input
                    id="imageAlt"
                    placeholder="Describe the image for accessibility"
                    value={imageAlt}
                    onChange={(e) => setImageAlt(e.target.value)}
                    maxLength={100}
                  />
                </div>
                <div>
                  <Label htmlFor="imageCredit">Image Credit</Label>
                  <Input
                    id="imageCredit"
                    placeholder="Photo credit or source"
                    value={imageCredit}
                    onChange={(e) => setImageCredit(e.target.value)}
                    maxLength={200}
                  />
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* File size info */}
      <p className="text-xs text-gray-500">
        Maximum file size: {maxFileSizeMB}MB. Supported formats: JPEG, PNG, GIF, WebP, SVG
      </p>
    </div>
  );
}
