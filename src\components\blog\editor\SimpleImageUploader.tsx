'use client';

import { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Upload,
  Image as ImageIcon,
  Cloud,
  FolderOpen,
  Link
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface SimpleImageUploaderProps {
  label: string;
  onImageUpload: (imageData: {
    url: string;
    alt?: string;
    title?: string;
  }) => void;
  maxFileSizeMB?: number;
  className?: string;
}

export function SimpleImageUploader({
  label,
  onImageUpload,
  maxFileSizeMB = 50,
  className = '',
}: SimpleImageUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [dragActive, setDragActive] = useState(false);
  const [showUrlModal, setShowUrlModal] = useState(false);
  const [showGoogleDriveModal, setShowGoogleDriveModal] = useState(false);
  const [showDropboxModal, setShowDropboxModal] = useState(false);
  const [imageUrl, setImageUrl] = useState('');

  const fileInputRef = useRef<HTMLInputElement>(null);
  const maxSizeBytes = maxFileSizeMB * 1024 * 1024;

  // Handle drag events
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  // Handle drop event
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  }, []);

  // Handle file input change
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();

    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files);
    }
  }, []);

  // Process files
  const handleFiles = useCallback((files: FileList) => {
    const file = files[0];

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: 'Invalid file type',
        description: 'Please upload a JPEG, PNG, GIF, SVG, or WebP file.',
        variant: 'destructive',
      });
      return;
    }

    // Check file size
    if (file.size > maxSizeBytes) {
      toast({
        title: 'File too large',
        description: `Please upload an image smaller than ${maxFileSizeMB}MB.`,
        variant: 'destructive',
      });
      return;
    }

    // Upload the file
    uploadFile(file);
  }, [maxSizeBytes, maxFileSizeMB]);

  // Upload file to server
  const uploadFile = async (file: File) => {
    setIsUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'image');

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 10;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 300);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to upload image');
      }

      const data = await response.json();

      // Wait a moment to show 100% progress
      setTimeout(() => {
        setIsUploading(false);
        setUploadProgress(0);

        toast({
          title: 'Image uploaded',
          description: 'Your image has been uploaded successfully.',
        });

        // Call the callback with the image URL
        onImageUpload({
          url: data.fileUrl,
          alt: file.name.split('.')[0],
          title: file.name.split('.')[0],
        });
      }, 500);

    } catch (error) {
      setIsUploading(false);
      setUploadProgress(0);

      const errorMessage = error instanceof Error ? error.message : 'Failed to upload image';

      toast({
        title: 'Upload failed',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  // Upload from URL
  const uploadFromUrl = async () => {
    if (!imageUrl.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a valid image URL.',
        variant: 'destructive',
      });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 10;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 300);

      // For now, just use the URL directly (in production, you'd want to download and re-upload)
      clearInterval(progressInterval);
      setUploadProgress(100);

      setTimeout(() => {
        setIsUploading(false);
        setUploadProgress(0);
        setShowUrlModal(false);

        toast({
          title: 'Image added',
          description: 'Image URL has been added successfully.',
        });

        // Call the callback with the image URL
        onImageUpload({
          url: imageUrl,
          alt: 'Image from URL',
          title: 'Image from URL',
        });

        setImageUrl('');
      }, 500);

    } catch (error) {
      setIsUploading(false);
      setUploadProgress(0);

      toast({
        title: 'Failed to add image',
        description: 'Please check the URL and try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <Label>{label}</Label>

      {/* Drag and drop area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 transition-all ${
          dragActive
            ? 'border-primary bg-primary/5'
            : 'border-muted-foreground/25 hover:border-primary/50'
        } ${
          isUploading ? 'opacity-50 pointer-events-none' : ''
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <div className="flex flex-col items-center justify-center gap-4">
          <ImageIcon className="h-16 w-16 text-muted-foreground" />
          <p className="text-center text-muted-foreground">
            Drag and drop an image here, or choose an upload method
          </p>

          <div className="flex flex-wrap gap-2 justify-center">
            <Button
              type="button"
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
              className="flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              Select File
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={() => setShowUrlModal(true)}
              disabled={isUploading}
              className="flex items-center gap-2"
            >
              <Link className="h-4 w-4" />
              Image URL
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={() => setShowGoogleDriveModal(true)}
              disabled={isUploading}
              className="flex items-center gap-2"
            >
              <Cloud className="h-4 w-4" />
              Google Drive
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={() => setShowDropboxModal(true)}
              disabled={isUploading}
              className="flex items-center gap-2"
            >
              <FolderOpen className="h-4 w-4" />
              Dropbox
            </Button>
          </div>
        </div>

        <Input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/png,image/gif,image/svg+xml,image/webp"
          onChange={handleChange}
          className="hidden"
        />
      </div>

      {/* Upload progress */}
      {isUploading && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Uploading...</span>
            <span>{Math.round(uploadProgress)}%</span>
          </div>
          <Progress value={uploadProgress} />
        </div>
      )}

      {/* URL Upload Modal */}
      <Dialog open={showUrlModal} onOpenChange={setShowUrlModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Image from URL</DialogTitle>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="image-url">Image URL</Label>
              <Input
                id="image-url"
                type="url"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                placeholder="https://example.com/image.jpg"
                disabled={isUploading}
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setShowUrlModal(false);
                  setImageUrl('');
                }}
                disabled={isUploading}
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={uploadFromUrl}
                disabled={isUploading || !imageUrl.trim()}
              >
                {isUploading ? 'Adding...' : 'Add Image'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Google Drive Modal */}
      <Dialog open={showGoogleDriveModal} onOpenChange={setShowGoogleDriveModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Select from Google Drive</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-center text-muted-foreground">
              Google Drive integration will be available soon.
            </p>
          </div>
        </DialogContent>
      </Dialog>

      {/* Dropbox Modal */}
      <Dialog open={showDropboxModal} onOpenChange={setShowDropboxModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Select from Dropbox</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-center text-muted-foreground">
              Dropbox integration will be available soon.
            </p>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
