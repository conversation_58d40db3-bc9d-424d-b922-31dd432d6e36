'use client';

import { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Image as ImageIcon,
  Upload,
  Link,
  Cloud,
  FolderOpen,
  ChevronDown,
  Loader2,
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { Progress } from '@/components/ui/progress';

interface ToolbarImageUploaderProps {
  onImageInsert: (imageData: {
    url: string;
    alt?: string;
    title?: string;
    width?: number;
    height?: number;
  }) => void;
}

type UploadDestination = 'cloudinary' | 'vps';
type UploadMethod = 'file' | 'url' | 'drive' | 'dropbox';

export function ToolbarImageUploader({ onImageInsert }: ToolbarImageUploaderProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [uploadMethod, setUploadMethod] = useState<UploadMethod>('file');
  const [uploadDestination, setUploadDestination] = useState<UploadDestination>('cloudinary');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [imageUrl, setImageUrl] = useState('');
  const [imageAlt, setImageAlt] = useState('');
  const [imageTitle, setImageTitle] = useState('');
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Allowed file types
  const allowedFileTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp',
  ];

  const maxFileSize = 5 * 1024 * 1024; // 5MB

  const validateFile = (file: File): boolean => {
    if (!allowedFileTypes.includes(file.type)) {
      toast({
        title: 'Invalid file type',
        description: 'Please upload a JPEG, PNG, or WebP file.',
        variant: 'destructive',
      });
      return false;
    }

    if (file.size > maxFileSize) {
      toast({
        title: 'File too large',
        description: 'Please upload an image smaller than 5MB.',
        variant: 'destructive',
      });
      return false;
    }

    return true;
  };

  const validateUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      toast({
        title: 'Invalid URL',
        description: 'Please enter a valid image URL.',
        variant: 'destructive',
      });
      return false;
    }
  };

  const sanitizeFilename = (filename: string): string => {
    return filename
      .replace(/[^a-zA-Z0-9.-]/g, '_')
      .replace(/_{2,}/g, '_')
      .toLowerCase();
  };

  const uploadToDestination = async (file: File | string, method: UploadMethod): Promise<any> => {
    const endpoint = uploadDestination === 'cloudinary' 
      ? '/api/upload/cloudinary' 
      : '/api/upload/local';

    if (method === 'file' && file instanceof File) {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'blog-content');

      return fetch(endpoint, {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });
    } else if (method === 'url' && typeof file === 'string') {
      return fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: file,
          type: 'blog-content',
        }),
        credentials: 'include',
      });
    }

    throw new Error('Invalid upload method or file type');
  };

  const handleFileUpload = async (file: File) => {
    if (!validateFile(file)) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 10;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 300);

      const response = await uploadToDestination(file, 'file');
      
      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to upload image');
      }

      const result = await response.json();
      
      onImageInsert({
        url: result.url || result.fileUrl,
        alt: imageAlt,
        title: imageTitle,
        width: result.width,
        height: result.height,
      });

      toast({
        title: 'Image uploaded successfully',
        description: `Image uploaded to ${uploadDestination === 'cloudinary' ? 'Cloudinary' : 'VPS'}.`,
      });

      resetDialog();
    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: 'Upload failed',
        description: error instanceof Error ? error.message : 'Failed to upload image',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleUrlUpload = async () => {
    if (!imageUrl.trim() || !validateUrl(imageUrl.trim())) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 15;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 200);

      const response = await uploadToDestination(imageUrl.trim(), 'url');
      
      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to upload image from URL');
      }

      const result = await response.json();
      
      onImageInsert({
        url: result.url || result.fileUrl,
        alt: imageAlt,
        title: imageTitle,
        width: result.width,
        height: result.height,
      });

      toast({
        title: 'Image uploaded successfully',
        description: `Image uploaded from URL to ${uploadDestination === 'cloudinary' ? 'Cloudinary' : 'VPS'}.`,
      });

      resetDialog();
    } catch (error) {
      console.error('URL upload error:', error);
      toast({
        title: 'Upload failed',
        description: error instanceof Error ? error.message : 'Failed to upload image from URL',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const resetDialog = () => {
    setIsDialogOpen(false);
    setImageUrl('');
    setImageAlt('');
    setImageTitle('');
    setUploadProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const openUploadDialog = (method: UploadMethod) => {
    setUploadMethod(method);
    setIsDialogOpen(true);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageTitle(file.name.split('.')[0]);
      handleFileUpload(file);
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            type="button"
            size="sm"
            variant="ghost"
            className="h-7 px-2 gap-1"
            title="Insert Image"
          >
            <ImageIcon className="h-3.5 w-3.5" />
            <ChevronDown className="h-3 w-3" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-56">
          <div className="px-2 py-1.5 text-sm font-medium">Upload Image</div>
          <DropdownMenuSeparator />
          
          <DropdownMenuItem onClick={() => openUploadDialog('file')}>
            <Upload className="h-4 w-4 mr-2" />
            Upload from Local Filesystem
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={() => openUploadDialog('url')}>
            <Link className="h-4 w-4 mr-2" />
            Upload by URL
          </DropdownMenuItem>
          
          <DropdownMenuItem disabled>
            <Cloud className="h-4 w-4 mr-2" />
            Upload from Google Drive
            <span className="ml-auto text-xs text-muted-foreground">Soon</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem disabled>
            <FolderOpen className="h-4 w-4 mr-2" />
            Upload from Dropbox
            <span className="ml-auto text-xs text-muted-foreground">Soon</span>
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          <div className="px-2 py-1.5">
            <Label className="text-xs text-muted-foreground">Upload to:</Label>
            <Select value={uploadDestination} onValueChange={(value: UploadDestination) => setUploadDestination(value)}>
              <SelectTrigger className="h-7 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="cloudinary">Cloudinary</SelectItem>
                <SelectItem value="vps">VPS (Local Storage)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {uploadMethod === 'file' ? 'Upload from Local Filesystem' :
               uploadMethod === 'url' ? 'Upload by URL' :
               uploadMethod === 'drive' ? 'Upload from Google Drive' :
               'Upload from Dropbox'}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            {/* Upload Progress */}
            {isUploading && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm">Uploading to {uploadDestination === 'cloudinary' ? 'Cloudinary' : 'VPS'}...</span>
                </div>
                <Progress value={uploadProgress} className="w-full" />
              </div>
            )}

            {/* Upload Method Content */}
            {uploadMethod === 'file' && (
              <div className="space-y-4">
                <div>
                  <Label>Select Image File</Label>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept={allowedFileTypes.join(',')}
                    onChange={handleFileSelect}
                    className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
                    disabled={isUploading}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Supported: JPEG, PNG, WebP. Max size: 5MB
                  </p>
                </div>
              </div>
            )}

            {uploadMethod === 'url' && (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="imageUrl">Image URL</Label>
                  <Input
                    id="imageUrl"
                    type="url"
                    placeholder="https://example.com/image.jpg"
                    value={imageUrl}
                    onChange={(e) => setImageUrl(e.target.value)}
                    disabled={isUploading}
                  />
                </div>
                <Button
                  onClick={handleUrlUpload}
                  disabled={isUploading || !imageUrl.trim()}
                  className="w-full"
                >
                  {isUploading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    'Upload from URL'
                  )}
                </Button>
              </div>
            )}

            {/* Image Metadata */}
            <div className="space-y-3 border-t pt-4">
              <div>
                <Label htmlFor="imageAlt">Alt Text (Optional)</Label>
                <Input
                  id="imageAlt"
                  placeholder="Describe the image for accessibility"
                  value={imageAlt}
                  onChange={(e) => setImageAlt(e.target.value)}
                  maxLength={100}
                  disabled={isUploading}
                />
              </div>
              <div>
                <Label htmlFor="imageTitle">Title (Optional)</Label>
                <Input
                  id="imageTitle"
                  placeholder="Image title"
                  value={imageTitle}
                  onChange={(e) => setImageTitle(e.target.value)}
                  maxLength={100}
                  disabled={isUploading}
                />
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
