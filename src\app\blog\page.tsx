"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import { Calendar, ArrowRight, Search, ChevronDown, ChevronRight, Eye, User, Tag as TagIcon } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { getAllPosts, searchPosts, getCategories, getTags, getRecentPosts } from "@/services/blogService";
import { useEffect, useState } from "react";
import { useTheme } from "@/hooks/useTheme";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Header from "@/components/layout/Header";

export default function BlogPage() {
  const { theme } = useTheme();
  const searchParams = useSearchParams();
  const searchQuery = searchParams.get("q") || "";
  const categoryFilter = searchParams.get("category") || "";
  const tagFilter = searchParams.get("tag") || "";

  // State management
  const [displayedPosts, setDisplayedPosts] = useState(getAllPosts());
  const [searchTerm, setSearchTerm] = useState(searchQuery);
  const [currentPage, setCurrentPage] = useState(1);
  const [categoriesOpen, setCategoriesOpen] = useState(false);
  const [recentPostsOpen, setRecentPostsOpen] = useState(false);
  const [tagsOpen, setTagsOpen] = useState(false);

  const postsPerPage = 6;
  const maxPosts = 12; // Show only 12 most recent posts (6 per page, 2 pages)

  // Get data
  const categories = getCategories();
  const tags = getTags();
  const recentPosts = getRecentPosts(6);

  // Live search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      // Only update search query if searchTerm is different from current searchQuery
      if (searchTerm !== searchQuery) {
        // Update URL with search query
        if (searchTerm.trim()) {
          window.history.replaceState({}, '', `/blog?q=${encodeURIComponent(searchTerm.trim())}`);
        } else {
          window.history.replaceState({}, '', '/blog');
        }
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchTerm, searchQuery]);

  useEffect(() => {
    let posts = getAllPosts();

    if (searchQuery || searchTerm) {
      const query = searchTerm || searchQuery;
      posts = searchPosts(query);
    } else if (categoryFilter) {
      posts = posts.filter(post =>
        post.category.toLowerCase() === categoryFilter.toLowerCase()
      );
    } else if (tagFilter) {
      posts = posts.filter(post =>
        post.tags?.some(tag => tag.toLowerCase() === tagFilter.toLowerCase())
      );
    }

    // Sort by date (newest first) and limit to 12 posts
    posts = posts
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, maxPosts);

    setDisplayedPosts(posts);
    setCurrentPage(1);
  }, [searchQuery, searchTerm, categoryFilter, tagFilter]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 300, damping: 24 }
    }
  };

  const sidebarVariants = {
    hidden: { x: 20, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: { delay: 0.3, duration: 0.5 }
    }
  };

  // Pagination logic
  const totalPages = Math.ceil(displayedPosts.length / postsPerPage);
  const startIndex = (currentPage - 1) * postsPerPage;
  const endIndex = startIndex + postsPerPage;
  const currentPosts = displayedPosts.slice(startIndex, endIndex);

  // Initialize search term from URL
  useEffect(() => {
    setSearchTerm(searchQuery);
  }, [searchQuery]);

  const isDark = theme === 'dark';

  return (
    <div className={`min-h-screen ${isDark
      ? 'bg-gradient-to-br from-gray-900 via-black to-gray-900'
      : 'bg-gradient-to-br from-blue-50 via-white to-purple-50'
    }`}>
      {/* Header */}
      <Header />

      {/* Hero Section with Search */}
      <motion.section
        className="py-16 px-4"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="max-w-7xl mx-auto text-center">
          <motion.h1
            className={`text-4xl md:text-5xl font-bold mb-6 ${isDark ? 'text-white' : 'text-gray-900'}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            📝 Our Blog
          </motion.h1>

          <motion.p
            className={`text-lg mb-8 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.6 }}
          >
            {searchQuery ? `Search results for "${searchQuery}"` :
             categoryFilter ? `Articles in ${categoryFilter}` :
             tagFilter ? `Articles tagged with "${tagFilter}"` :
             "Articles, guides, and tips about tools and productivity"}
          </motion.p>

          {/* Live Search Bar */}
          <motion.div
            className="max-w-md mx-auto mb-8"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                type="text"
                placeholder="Search articles... (live search)"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`pl-10 pr-4 py-3 w-full rounded-full border-2 transition-all duration-300 ${
                  isDark
                    ? 'bg-gray-800/50 border-gray-700 text-white placeholder-gray-400 focus:border-purple-500'
                    : 'bg-white/80 border-gray-200 text-gray-900 placeholder-gray-500 focus:border-blue-500'
                } backdrop-blur-sm`}
              />
              {searchTerm && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2"
                >
                  <div className={`w-2 h-2 rounded-full animate-pulse ${
                    isDark ? 'bg-purple-400' : 'bg-blue-500'
                  }`} />
                </motion.div>
              )}
            </div>
          </motion.div>

          {/* View All Articles Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.6 }}
          >
            <Link href="/blog/all">
              <Button
                className={`rounded-full px-8 py-3 font-medium transition-all duration-300 ${
                  isDark
                    ? 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white'
                    : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white'
                } shadow-lg hover:shadow-xl transform hover:scale-105`}
              >
                View All Articles
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </motion.div>
        </div>
      </motion.section>

      {/* Main Content */}
      <section className="py-12 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">

            {/* Blog Posts - Main Content */}
            <div className="lg:col-span-3">
              {currentPosts.length === 0 ? (
                <motion.div
                  className="text-center py-12"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className={`mx-auto h-12 w-12 mb-4 rounded-full flex items-center justify-center ${
                    isDark ? 'bg-gray-800 text-gray-400' : 'bg-gray-100 text-gray-500'
                  }`}>
                    <Search className="h-6 w-6" />
                  </div>
                  <h3 className={`text-xl font-medium mb-2 ${isDark ? 'text-white' : 'text-gray-900'}`}>
                    No posts found
                  </h3>
                  <p className={`${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                    {searchQuery ?
                      `No posts match your search for "${searchQuery}"` :
                      "No posts available at the moment"}
                  </p>
                </motion.div>
              ) : (
                <>
                  <motion.div
                    className="grid grid-cols-1 md:grid-cols-2 gap-8"
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                  >
                    {currentPosts.map((post) => (
                      <motion.article
                        key={post.id}
                        className={`rounded-2xl overflow-hidden transition-all duration-300 hover:scale-105 ${
                          isDark
                            ? 'bg-gradient-to-br from-gray-800/50 to-gray-900/50 border border-gray-700/50 backdrop-blur-sm'
                            : 'bg-white/80 border border-gray-200/50 backdrop-blur-sm shadow-lg hover:shadow-xl'
                        }`}
                        variants={itemVariants}
                        whileHover={{ y: -5 }}
                        transition={{ type: "spring", stiffness: 300, damping: 24 }}
                      >
                        <Link href={`/blog/${post.id}`}>
                          <div className="relative h-48 overflow-hidden">
                            <motion.img
                              src={post.image}
                              alt={post.title}
                              className="w-full h-full object-cover"
                              whileHover={{ scale: 1.1 }}
                              transition={{ duration: 0.6 }}
                            />
                            <div className="absolute top-4 left-4">
                              <Badge className={`${
                                isDark
                                  ? 'bg-purple-600/90 text-white'
                                  : 'bg-blue-600/90 text-white'
                              } backdrop-blur-sm`}>
                                {post.category}
                              </Badge>
                            </div>
                            <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-black/70 to-transparent" />
                          </div>
                        </Link>

                        <div className="p-6">
                          <div className={`flex items-center text-xs gap-4 mb-3 ${
                            isDark ? 'text-gray-400' : 'text-gray-500'
                          }`}>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              <span>{post.date}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              <span>{post.author}</span>
                            </div>
                          </div>

                          <Link href={`/blog/${post.id}`}>
                            <h2 className={`text-xl font-bold mb-3 transition-colors ${
                              isDark
                                ? 'text-white hover:text-purple-400'
                                : 'text-gray-900 hover:text-blue-600'
                            }`}>
                              {post.title}
                            </h2>
                          </Link>

                          <p className={`mb-4 line-clamp-2 ${
                            isDark ? 'text-gray-300' : 'text-gray-600'
                          }`}>
                            {post.description || ""}
                          </p>

                          <Link
                            href={`/blog/${post.id}`}
                            className={`inline-flex items-center text-sm font-medium transition-colors ${
                              isDark
                                ? 'text-purple-400 hover:text-purple-300'
                                : 'text-blue-600 hover:text-blue-700'
                            }`}
                          >
                            Read More
                            <ArrowRight className="h-4 w-4 ml-1" />
                          </Link>
                        </div>
                      </motion.article>
                    ))}
                  </motion.div>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <motion.div
                      className="flex justify-center items-center gap-4 mt-12"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.6, duration: 0.5 }}
                    >
                      <Button
                        variant="outline"
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                        className={`${isDark ? 'border-gray-700 text-gray-300 hover:bg-gray-800' : 'border-gray-300'}`}
                      >
                        Previous
                      </Button>

                      <div className="flex gap-2">
                        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                          <Button
                            key={page}
                            variant={currentPage === page ? "default" : "outline"}
                            onClick={() => setCurrentPage(page)}
                            className={`w-10 h-10 ${
                              currentPage === page
                                ? (isDark ? 'bg-purple-600 text-white' : 'bg-blue-600 text-white')
                                : (isDark ? 'border-gray-700 text-gray-300 hover:bg-gray-800' : 'border-gray-300')
                            }`}
                          >
                            {page}
                          </Button>
                        ))}
                      </div>

                      <Button
                        variant="outline"
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        disabled={currentPage === totalPages}
                        className={`${isDark ? 'border-gray-700 text-gray-300 hover:bg-gray-800' : 'border-gray-300'}`}
                      >
                        Next
                      </Button>
                    </motion.div>
                  )}

                  {/* View All Articles Button at Bottom */}
                  <motion.div
                    className="text-center mt-12"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7, duration: 0.5 }}
                  >
                    <Link href="/blog/all">
                      <Button
                        variant="outline"
                        className={`rounded-full px-8 py-3 font-medium transition-all duration-300 ${
                          isDark
                            ? 'border-purple-600 text-purple-400 hover:bg-purple-600 hover:text-white'
                            : 'border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white'
                        } shadow-lg hover:shadow-xl transform hover:scale-105`}
                      >
                        All Blog Articles
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </Link>
                  </motion.div>
                </>
              )}
            </div>

            {/* Sidebar */}
            <motion.aside
              className="lg:col-span-1"
              variants={sidebarVariants}
              initial="hidden"
              animate="visible"
            >
              <div className="space-y-6">

                {/* Categories Dropdown */}
                <Card className={`${
                  isDark
                    ? 'bg-gradient-to-br from-gray-800/50 to-gray-900/50 border-gray-700/50 backdrop-blur-sm'
                    : 'bg-white/80 border-gray-200/50 backdrop-blur-sm shadow-lg'
                }`}>
                  <CardContent className="p-6">
                    <motion.button
                      onClick={() => setCategoriesOpen(!categoriesOpen)}
                      className={`w-full flex items-center justify-between text-left font-semibold text-lg ${
                        isDark ? 'text-white' : 'text-gray-900'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <span className="flex items-center gap-2">
                        <TagIcon className="h-5 w-5" />
                        Categories
                      </span>
                      <motion.div
                        animate={{ rotate: categoriesOpen ? 90 : 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <ChevronRight className="h-5 w-5" />
                      </motion.div>
                    </motion.button>

                    <motion.div
                      initial={false}
                      animate={{
                        height: categoriesOpen ? "auto" : 0,
                        opacity: categoriesOpen ? 1 : 0
                      }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="mt-4 space-y-2">
                        {categories.map((category) => (
                          <Link
                            key={category.name}
                            href={`/blog?category=${encodeURIComponent(category.name)}`}
                            className={`block p-2 rounded-lg transition-colors ${
                              isDark
                                ? 'hover:bg-gray-700/50 text-gray-300 hover:text-white'
                                : 'hover:bg-gray-100 text-gray-600 hover:text-gray-900'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <span>{category.name}</span>
                              <Badge variant="secondary" className="text-xs">
                                {category.count}
                              </Badge>
                            </div>
                          </Link>
                        ))}
                      </div>
                    </motion.div>
                  </CardContent>
                </Card>

                {/* Recent Posts Dropdown */}
                <Card className={`${
                  isDark
                    ? 'bg-gradient-to-br from-gray-800/50 to-gray-900/50 border-gray-700/50 backdrop-blur-sm'
                    : 'bg-white/80 border-gray-200/50 backdrop-blur-sm shadow-lg'
                }`}>
                  <CardContent className="p-6">
                    <motion.button
                      onClick={() => setRecentPostsOpen(!recentPostsOpen)}
                      className={`w-full flex items-center justify-between text-left font-semibold text-lg ${
                        isDark ? 'text-white' : 'text-gray-900'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <span className="flex items-center gap-2">
                        <Calendar className="h-5 w-5" />
                        Recent Posts
                      </span>
                      <motion.div
                        animate={{ rotate: recentPostsOpen ? 90 : 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <ChevronRight className="h-5 w-5" />
                      </motion.div>
                    </motion.button>

                    <motion.div
                      initial={false}
                      animate={{
                        height: recentPostsOpen ? "auto" : 0,
                        opacity: recentPostsOpen ? 1 : 0
                      }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="mt-4 space-y-3">
                        {recentPosts.map((post) => (
                          <Link
                            key={post.slug}
                            href={`/blog/${post.slug}`}
                            className={`block p-3 rounded-lg transition-colors ${
                              isDark
                                ? 'hover:bg-gray-700/50 text-gray-300 hover:text-white'
                                : 'hover:bg-gray-100 text-gray-600 hover:text-gray-900'
                            }`}
                          >
                            <h4 className="font-medium text-sm line-clamp-2">
                              {post.title}
                            </h4>
                          </Link>
                        ))}
                      </div>
                    </motion.div>
                  </CardContent>
                </Card>

                {/* Tags Dropdown */}
                <Card className={`${
                  isDark
                    ? 'bg-gradient-to-br from-gray-800/50 to-gray-900/50 border-gray-700/50 backdrop-blur-sm'
                    : 'bg-white/80 border-gray-200/50 backdrop-blur-sm shadow-lg'
                }`}>
                  <CardContent className="p-6">
                    <motion.button
                      onClick={() => setTagsOpen(!tagsOpen)}
                      className={`w-full flex items-center justify-between text-left font-semibold text-lg ${
                        isDark ? 'text-white' : 'text-gray-900'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <span className="flex items-center gap-2">
                        <TagIcon className="h-5 w-5" />
                        Tags
                      </span>
                      <motion.div
                        animate={{ rotate: tagsOpen ? 90 : 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <ChevronRight className="h-5 w-5" />
                      </motion.div>
                    </motion.button>

                    <motion.div
                      initial={false}
                      animate={{
                        height: tagsOpen ? "auto" : 0,
                        opacity: tagsOpen ? 1 : 0
                      }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="mt-4 flex flex-wrap gap-2">
                        {tags.map((tag) => (
                          <Link
                            key={tag.name}
                            href={`/blog?tag=${encodeURIComponent(tag.name)}`}
                          >
                            <Badge
                              variant="outline"
                              className={`transition-colors cursor-pointer ${
                                isDark
                                  ? 'border-gray-600 text-gray-300 hover:bg-purple-600 hover:text-white hover:border-purple-600'
                                  : 'border-gray-300 text-gray-600 hover:bg-blue-600 hover:text-white hover:border-blue-600'
                              }`}
                            >
                              {tag.name} ({tag.count})
                            </Badge>
                          </Link>
                        ))}
                      </div>
                    </motion.div>
                  </CardContent>
                </Card>

              </div>
            </motion.aside>

          </div>
        </div>
      </section>
    </div>
  );
}